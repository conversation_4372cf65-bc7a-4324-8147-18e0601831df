import uuid
from django.db import models


class DownloadTask(models.Model):
    """
    Model to track download task status and information
    """

    class TaskStatus(models.TextChoices):
        PENDING = 'pending', 'Pending'
        PROCESSING = 'processing', 'Processing'
        COMPLETED = 'completed', 'Completed'
        FAILED = 'failed', 'Failed'

    task_id = models.UUIDField(default=uuid.uuid4, unique=True, editable=False)
    status = models.CharField(
        max_length=20,
        choices=TaskStatus.choices,
        default=TaskStatus.PENDING
    )
    url = models.URLField()
    hosting = models.CharField(max_length=50, default='instagram')
    bot_token = models.CharField(max_length=255)
    bot_username = models.Char<PERSON>ield(max_length=100, blank=True, null=True)
    chat_id = models.CharField(max_length=100)

    # Tracking fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Error handling
    error_message = models.TextField(blank=True, null=True)

    # Processing details
    account_name = models.CharField(max_length=100, blank=True, null=True)
    message_id = models.BigIntegerField(blank=True, null=True)
    file_id = models.TextField(blank=True, null=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Task {self.task_id} - {self.status}"

    def mark_processing(self, account_name=None, message_id=None):
        """Mark task as processing"""
        self.status = self.TaskStatus.PROCESSING
        if account_name:
            self.account_name = account_name
        if message_id:
            self.message_id = message_id
        self.save()

    def mark_completed(self, file_id=None):
        """Mark task as completed"""
        self.status = self.TaskStatus.COMPLETED
        self.error_message = None
        if file_id:
            self.file_id = file_id
        self.save()

    def mark_failed(self, error_message=None):
        """Mark task as failed"""
        self.status = self.TaskStatus.FAILED
        if error_message:
            self.error_message = error_message
        self.save()
